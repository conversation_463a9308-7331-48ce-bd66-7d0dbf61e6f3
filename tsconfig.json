{"compilerOptions": {"target": "es2017", "module": "commonjs", "lib": ["es2017", "esnext.asynciterable"], "typeRoots": ["./node_modules/@types", "./src/types"], "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "outDir": "./dist", "esModuleInterop": true, "strict": true, "skipLibCheck": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}