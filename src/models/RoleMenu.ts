import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { Role } from './Role';
import { Menu } from './Menu';

@Entity()
export class RoleMenu {
  @PrimaryGeneratedColumn()
  id!: number;

  @ManyToOne(() => Role, role => role.roleMenus, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'role_id' })
  role!: Role;

  @ManyToOne(() => Menu, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'menu_id' })
  menu!: Menu;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
} 