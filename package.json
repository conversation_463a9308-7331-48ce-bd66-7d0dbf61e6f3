{"name": "koa-ts-postgres-backend", "version": "1.0.0", "description": "Koa TypeScript PostgreSQL Backend Boilerplate", "main": "dist/index.js", "_moduleAliases": {"@": "src"}, "engines": {"node": ">=22.14.0", "npm": ">=10.2.4"}, "scripts": {"start": "node -r module-alias/register dist/index.js", "dev": "cross-env NODE_ENV=development nodemon -r module-alias/register", "build": "tsc && tsc-alias", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "test": "jest", "migration:generate": "npm run typeorm migration:generate -- -d src/database/connection.ts", "migration:create": "npm run typeorm migration:create", "migration:run": "npm run typeorm migration:run -- -d src/database/connection.ts", "migration:revert": "npm run typeorm migration:revert -- -d src/database/connection.ts", "migration:show": "npm run typeorm migration:show -- -d src/database/connection.ts"}, "dependencies": {"@koa/cors": "^4.0.0", "@types/bcrypt": "^5.0.2", "axios": "^1.8.4", "bcrypt": "^5.1.1", "cheerio": "^1.0.0", "dayjs": "^1.11.13", "dotenv": "^16.0.3", "jsonwebtoken": "^9.0.2", "koa": "^2.14.2", "koa-bodyparser": "^4.4.1", "koa-helmet": "^7.1.0", "koa-router": "^12.0.0", "module-alias": "^2.2.3", "pg": "^8.11.0", "typeorm": "^0.3.16", "winston": "^3.8.2"}, "devDependencies": {"@types/jest": "^29.5.1", "@types/jsonwebtoken": "^9.0.9", "@types/koa": "^2.13.6", "@types/koa-bodyparser": "^4.3.10", "@types/koa-router": "^7.4.4", "@types/koa__cors": "^4.0.0", "@types/node": "^20.1.0", "@types/pg": "^8.6.6", "@typescript-eslint/eslint-plugin": "^5.59.2", "@typescript-eslint/parser": "^5.59.2", "cross-env": "^7.0.3", "eslint": "^8.40.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "nodemon": "^3.1.9", "prettier": "^2.8.8", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "tsc-alias": "^1.8.8", "typescript": "^5.0.4"}}